package cn.yanz<PERSON>hui;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * 使用libheif命令行工具进行HEIC转换的Java包装器
 * 需要安装libheif-examples包
 */
public class HeicConverterWithLibheif {
    
    public static void main(String[] args) {
        String inputPath;
        String outputPath;
        
        // 处理命令行参数
        if (args.length >= 2) {
            inputPath = args[0];
            outputPath = args[1];
        } else if (args.length == 1) {
            inputPath = args[0];
            outputPath = generateOutputPath(inputPath);
        } else {
            inputPath = "./file/Flower.HEIC";
            outputPath = "./file/Flower.PNG";
            System.out.println("使用默认文件路径进行转换...");
        }
        
        convertHeicToPngWithLibheif(inputPath, outputPath);
    }
    
    /**
     * 使用libheif命令行工具将HEIC转换为PNG
     */
    public static void convertHeicToPngWithLibheif(String inputPath, String outputPath) {
        try {
            // 检查输入文件是否存在
            File inputFile = new File(inputPath);
            if (!inputFile.exists()) {
                System.err.println("输入文件不存在: " + inputPath);
                return;
            }
            
            System.out.println("使用libheif进行转换...");
            System.out.println("输入文件: " + inputPath);
            System.out.println("输出文件: " + outputPath);
            
            // 创建输出目录
            File outputFile = new File(outputPath);
            File parentDir = outputFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            // 构建命令
            String[] command = {
                "heif-convert", 
                inputPath, 
                outputPath
            };
            
            // 执行命令
            ProcessBuilder pb = new ProcessBuilder(command);
            pb.redirectErrorStream(true);
            Process process = pb.start();
            
            // 读取输出
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println(line);
            }
            
            // 等待进程完成
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                System.out.println("✓ 成功将HEIC转换为PNG！");
                if (outputFile.exists()) {
                    System.out.println("输出文件大小: " + outputFile.length() + " bytes");
                }
            } else {
                System.err.println("转换失败，退出代码: " + exitCode);
                System.err.println("请确保已安装libheif-examples:");
                System.err.println("Windows: 下载libheif二进制文件");
                System.err.println("Linux: sudo apt install libheif-examples");
                System.err.println("macOS: brew install libheif");
            }
            
        } catch (IOException | InterruptedException e) {
            System.err.println("执行命令时发生错误: " + e.getMessage());
            System.err.println("请确保已安装libheif命令行工具");
            e.printStackTrace();
        }
    }
    
    /**
     * 根据输入文件路径生成输出文件路径
     */
    private static String generateOutputPath(String inputPath) {
        int lastDotIndex = inputPath.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return inputPath.substring(0, lastDotIndex) + ".PNG";
        } else {
            return inputPath + ".PNG";
        }
    }
}

@echo off
echo 正在下载HEIF ImageIO库...

REM 创建lib目录
if not exist "lib" mkdir lib

REM 下载imageio-heif库 (这些是示例URL，实际需要从Maven Central获取)
echo 下载imageio-heif-1.0.0.jar...
curl -L -o "lib/imageio-heif-1.0.0.jar" "https://repo1.maven.org/maven2/com/github/gotson/nightmonkeys/imageio-heif/1.0.0/imageio-heif-1.0.0.jar"

echo 下载相关依赖...
curl -L -o "lib/imageio-core-3.10.1.jar" "https://repo1.maven.org/maven2/com/twelvemonkeys/imageio/imageio-core/3.10.1/imageio-core-3.10.1.jar"

echo 下载完成！
echo.
echo 使用方法：
echo java -cp "lib/*;out/production" cn.yanzhonghui.Main [输入文件] [输出文件]
echo.
pause

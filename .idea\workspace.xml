<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f9782a87-b4de-42e3-9454-cf5b0b78e5a9" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="32rcgseKEAr7EXwGiCS3niqwFHk" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "last_opened_file_path": "C:/Users/<USER>/Downloads/HEIC-Convert-Java-master/HEIC-Convert-Java-master",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "项目",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "vue.rearranger.settings.migration": "true",
    "应用程序.HEICToJPGConverter.executor": "Run",
    "应用程序.HeicConverterWithLibheif.executor": "Run",
    "应用程序.Main.executor": "Run"
  }
}]]></component>
  <component name="RunManager" selected="应用程序.HEICToJPGConverter">
    <configuration name="HEICToJPGConverter" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="cn.yanzhonghui.HEICToJPGConverter" />
      <module name="heic-converter" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="cn.yanzhonghui.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="HeicConverterWithLibheif" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="cn.yanzhonghui.HeicConverterWithLibheif" />
      <module name="heic-converter" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="cn.yanzhonghui.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Main" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="ALTERNATIVE_JRE_PATH" value="17" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="MAIN_CLASS_NAME" value="cn.yanzhonghui.Main" />
      <module name="heic-converter" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="cn.yanzhonghui.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.HEICToJPGConverter" />
        <item itemvalue="应用程序.Main" />
        <item itemvalue="应用程序.HeicConverterWithLibheif" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="f9782a87-b4de-42e3-9454-cf5b0b78e5a9" name="更改" comment="" />
      <created>1758182245227</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1758182245227</updated>
      <workItem from="1758182246493" duration="1436000" />
      <workItem from="1758188852348" duration="2073000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>
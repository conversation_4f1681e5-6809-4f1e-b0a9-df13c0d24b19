package cn.ya<PERSON><PERSON><PERSON>;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Iterator;


public class HEICToJPGConverter {

    public static void convertHEICToJPG(String heicFilePath, String jpgFilePath) throws IOException {
        // Register HEIC image readers
        ImageIO.scanForPlugins();

        File heicFile = new File(heicFilePath);
        if (!heicFile.exists()) {
            throw new IOException("Input file does not exist: " + heicFilePath);
        }

        System.out.println("Starting conversion...");
        System.out.println("Input file: " + heicFilePath);
        System.out.println("Output file: " + jpgFilePath);

        // Print available readers
        printAvailableReaders();

        BufferedImage image = null;

        try {
            // Try to read using ImageIO directly first
            image = ImageIO.read(heicFile);

            if (image == null) {
                // If direct reading fails, try with specific HEIF readers
                try (FileInputStream fis = new FileInputStream(heicFile);
                     ImageInputStream iis = ImageIO.createImageInputStream(fis)) {

                    Iterator<ImageReader> readers = ImageIO.getImageReaders(iis);
                    if (readers.hasNext()) {
                        ImageReader reader = readers.next();
                        try {
                            reader.setInput(iis);
                            image = reader.read(0);
                        } finally {
                            reader.dispose();
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("Error reading HEIC file: " + e.getMessage());
            throw new IOException("Failed to read HEIC file", e);
        }

        if (image == null) {
            throw new IOException("Could not read HEIC file. Possible reasons:\n" +
                    "1. Missing HEIC/HEIF ImageIO plugin\n" +
                    "2. File format is not supported\n" +
                    "3. File is corrupted\n" +
                    "4. HEIF native libraries are not available");
        }

        // Create output directory if it doesn't exist
        File jpgFile = new File(jpgFilePath);
        File parentDir = jpgFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }

        // Write BufferedImage to JPG file
        boolean success = ImageIO.write(image, "jpg", jpgFile);

        if (!success) {
            throw new IOException("Failed to write JPG file");
        }

        System.out.println("Image dimensions: " + image.getWidth() + "x" + image.getHeight());
        System.out.println("Output file size: " + jpgFile.length() + " bytes");
    }

    /**
     * Print available ImageIO readers
     */
    private static void printAvailableReaders() {
        System.out.println("\nAvailable ImageIO readers:");
        String[] readerFormats = ImageIO.getReaderFormatNames();
        for (String format : readerFormats) {
            System.out.println("- " + format);
        }
        System.out.println();
    }

    public static void main(String[] args) {
        String heicFilePath;
        String jpgFilePath;

        // Handle command line arguments
        if (args.length >= 2) {
            heicFilePath = args[0];
            jpgFilePath = args[1];
        } else if (args.length == 1) {
            heicFilePath = args[0];
            // Auto-generate output filename
            jpgFilePath = generateOutputPath(heicFilePath, "jpg");
        } else {
            // Use default paths
            heicFilePath = "./file/Flower.HEIC";
            jpgFilePath = "./file/Flower.jpg";
            System.out.println("Using default file paths for conversion...");
        }

        try {
            convertHEICToJPG(heicFilePath, jpgFilePath);
            System.out.println("✓ Conversion successful: " + jpgFilePath);
        } catch (IOException e) {
            System.err.println("✗ Conversion failed: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Generate output file path based on input file path
     * @param inputPath Input file path
     * @param extension Output file extension
     * @return Output file path
     */
    private static String generateOutputPath(String inputPath, String extension) {
        int lastDotIndex = inputPath.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return inputPath.substring(0, lastDotIndex) + "." + extension.toUpperCase();
        } else {
            return inputPath + "." + extension.toUpperCase();
        }
    }
}

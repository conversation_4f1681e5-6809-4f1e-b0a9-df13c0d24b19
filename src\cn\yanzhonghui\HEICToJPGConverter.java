package cn.yanz<PERSON><PERSON>;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import org.ddr.image.heif.HeifImageReader;
import org.ddr.image.heif.HeifImageReaderSpi;

import java.io.File;
import java.io.IOException;


public class HEICToJPGConverter {

    public static void convertHEICToJPG(String heicFilePath, String jpgFilePath) throws IOException {
        // 注册HEIC图片读取器
        ImageIO.scanForPlugins();

        // 创建HEIF图片读取器
        HeifImageReader reader = new HeifImageReader(new HeifImageReaderSpi());

        // 读取HEIC文件
        File heicFile = new File(heicFilePath);
        BufferedImage image = reader.read(0); // 读取第一帧

        // 将BufferedImage写入JPG文件
        ImageIO.write(image, "jpg", new File(jpgFilePath));

        // 关闭读取器
        reader.dispose();
    }

    public static void main(String[] args) {
        String heicFilePath = "C:\\Users\\<USER>\\Downloads\\HEIC-Convert-Java-master\\HEIC-Convert-Java-master\\file\\Flower.heic";
        String jpgFilePath = "C:\\Users\\<USER>\\Downloads\\HEIC-Convert-Java-master\\HEIC-Convert-Java-master\\file\\image.jpg";

        try {
            convertHEICToJPG(heicFilePath, jpgFilePath);
            System.out.println("转换成功: " + jpgFilePath);
        } catch (IOException e) {
            System.err.println("转换失败: " + e.getMessage());
        }
    }
}

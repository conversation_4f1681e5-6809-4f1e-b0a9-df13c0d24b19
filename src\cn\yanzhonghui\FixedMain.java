package cn.yanzhong<PERSON>;

import org.im4java.core.*;
import org.im4java.process.ProcessStarter;
import java.io.File;
import java.io.IOException;

public class FixedMain {

    public static void main(String[] args) {
        System.out.println("HEIC to PNG Converter using ImageMagick");
        System.out.println("=======================================");
        
        String inputPath = "./file/Flower.HEIC";
        String outputPath = "./file/Flower.PNG";
        
        // 检查输入文件
        File inputFile = new File(inputPath);
        if (!inputFile.exists()) {
            System.err.println("ERROR: Input file not found: " + inputFile.getAbsolutePath());
            return;
        }
        
        System.out.println("Input file: " + inputFile.getAbsolutePath());
        System.out.println("Output file: " + new File(outputPath).getAbsolutePath());
        System.out.println("Input file size: " + inputFile.length() + " bytes");
        System.out.println();
        
        // 尝试不同的方法
        boolean success = false;
        
        // 方法1: 使用系统PATH中的magick命令
        if (!success) {
            System.out.println("Method 1: Trying magick command from system PATH...");
            try {
                convertWithSystemMagick(inputPath, outputPath);
                success = true;
                System.out.println("✓ SUCCESS with magick command!");
            } catch (Exception e) {
                System.out.println("✗ Failed: " + e.getMessage());
            }
        }
        
        // 方法2: 使用系统PATH中的convert命令
        if (!success) {
            System.out.println("\nMethod 2: Trying convert command from system PATH...");
            try {
                convertWithSystemConvert(inputPath, outputPath);
                success = true;
                System.out.println("✓ SUCCESS with convert command!");
            } catch (Exception e) {
                System.out.println("✗ Failed: " + e.getMessage());
            }
        }
        
        // 方法3: 尝试常见的ImageMagick安装路径
        if (!success) {
            System.out.println("\nMethod 3: Trying common ImageMagick installation paths...");
            String[] paths = {
                "C:\\Program Files\\ImageMagick-7.1.2-Q16-HDRI",
                "C:\\Program Files\\ImageMagick-7.0.11-Q16-HDRI",
                "C:\\Program Files\\ImageMagick",
                "C:\\Program Files (x86)\\ImageMagick-7.1.2-Q16-HDRI",
                "C:\\Program Files (x86)\\ImageMagick-7.0.11-Q16-HDRI"
            };
            
            for (String path : paths) {
                File dir = new File(path);
                if (dir.exists()) {
                    System.out.println("Trying path: " + path);
                    try {
                        convertWithSpecificPath(path, inputPath, outputPath);
                        success = true;
                        System.out.println("✓ SUCCESS with path: " + path);
                        break;
                    } catch (Exception e) {
                        System.out.println("✗ Failed with path " + path + ": " + e.getMessage());
                    }
                }
            }
        }
        
        if (success) {
            File outputFile = new File(outputPath);
            if (outputFile.exists()) {
                System.out.println("\n🎉 FINAL SUCCESS!");
                System.out.println("Output file created: " + outputFile.getAbsolutePath());
                System.out.println("Output file size: " + outputFile.length() + " bytes");
            }
        } else {
            System.err.println("\n❌ ALL METHODS FAILED!");
            printInstallationInstructions();
        }
    }
    
    /**
     * 使用系统PATH中的magick命令
     */
    private static void convertWithSystemMagick(String inputPath, String outputPath) 
            throws IOException, InterruptedException, IM4JavaException {
        
        // 不设置搜索路径，让系统在PATH中查找
        ConvertCmd cmd = new ConvertCmd();
        
        IMOperation op = new IMOperation();
        op.addImage(inputPath);
        op.addImage(outputPath);
        
        cmd.run(op);
        
        checkOutputFile(outputPath);
    }
    
    /**
     * 使用系统PATH中的convert命令
     */
    private static void convertWithSystemConvert(String inputPath, String outputPath) 
            throws IOException, InterruptedException, IM4JavaException {
        
        ConvertCmd cmd = new ConvertCmd();
        
        IMOperation op = new IMOperation();
        op.addImage(inputPath);
        op.addImage(outputPath);
        
        cmd.run(op);
        
        checkOutputFile(outputPath);
    }
    
    /**
     * 使用指定路径的ImageMagick
     */
    private static void convertWithSpecificPath(String imageMagickPath, String inputPath, String outputPath) 
            throws IOException, InterruptedException, IM4JavaException {
        
        ConvertCmd cmd = new ConvertCmd();
        cmd.setSearchPath(imageMagickPath);
        
        IMOperation op = new IMOperation();
        op.addImage(inputPath);
        op.addImage(outputPath);
        
        cmd.run(op);
        
        checkOutputFile(outputPath);
    }
    
    /**
     * 检查输出文件是否创建成功
     */
    private static void checkOutputFile(String outputPath) throws IOException {
        File outputFile = new File(outputPath);
        if (!outputFile.exists() || outputFile.length() == 0) {
            throw new IOException("Output file was not created or is empty");
        }
    }
    
    /**
     * 打印安装说明
     */
    private static void printInstallationInstructions() {
        System.out.println("\n=== ImageMagick Installation Instructions ===");
        System.out.println("1. Download ImageMagick from: https://imagemagick.org/script/download.php#windows");
        System.out.println("2. During installation, make sure to check:");
        System.out.println("   ☑ Add application directory to your system path");
        System.out.println("   ☑ Install development headers and libraries for C and C++");
        System.out.println("3. After installation, restart your command prompt/IDE");
        System.out.println("4. Test by running: magick -version");
        System.out.println("\nAlternatively, you can:");
        System.out.println("- Use the PowerShell method (convert-heic.bat)");
        System.out.println("- Install ImageMagick via Chocolatey: choco install imagemagick");
        System.out.println("- Use Windows Subsystem for Linux (WSL) with ImageMagick");
    }
}

@echo off
echo HEIC转JPG转换器
echo ===============

REM 检查PowerShell是否可用
powershell -Command "Write-Host 'PowerShell is available'" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 错误：PowerShell不可用
    pause
    exit /b 1
)

REM 检查输入文件是否存在
if not exist "file\Flower.HEIC" (
    echo 错误：找不到输入文件 file\Flower.HEIC
    echo 请确保HEIC文件存在于file目录中
    pause
    exit /b 1
)

echo 找到输入文件：file\Flower.HEIC
echo.

echo 开始转换...
powershell -ExecutionPolicy Bypass -File "convert-heic.ps1"

if %ERRORLEVEL% EQ 0 (
    echo.
    echo 转换成功完成！
    if exist "file\Flower_converted.jpg" (
        echo 输出文件：file\Flower_converted.jpg
        for %%A in ("file\Flower_converted.jpg") do echo 文件大小：%%~zA 字节
    )
) else (
    echo.
    echo 转换失败！
    echo.
    echo 故障排除建议：
    echo 1. 从Microsoft Store安装"HEIF图像扩展"
    echo 2. 确保HEIC文件没有损坏
    echo 3. 尝试先在Windows照片应用中打开文件
)

echo.
pause

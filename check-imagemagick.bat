@echo off
echo ImageMagick安装检查
echo ==================

echo 检查ImageMagick是否在PATH中...
magick -version >nul 2>&1
if %ERRORLEVEL% EQ 0 (
    echo ✓ magick命令可用
    magick -version
    echo.
) else (
    echo ✗ magick命令不可用
)

convert -version >nul 2>&1
if %ERRORLEVEL% EQ 0 (
    echo ✓ convert命令可用
    convert -version
    echo.
) else (
    echo ✗ convert命令不可用
)

echo 检查常见安装路径...
set FOUND=0

if exist "C:\Program Files\ImageMagick-7.1.2-Q16-HDRI\magick.exe" (
    echo ✓ 找到: C:\Program Files\ImageMagick-7.1.2-Q16-HDRI\magick.exe
    set FOUND=1
)

if exist "C:\Program Files\ImageMagick-7.0.11-Q16-HDRI\magick.exe" (
    echo ✓ 找到: C:\Program Files\ImageMagick-7.0.11-Q16-HDRI\magick.exe
    set FOUND=1
)

if exist "C:\Program Files\ImageMagick\magick.exe" (
    echo ✓ 找到: C:\Program Files\ImageMagick\magick.exe
    set FOUND=1
)

if exist "C:\Program Files (x86)\ImageMagick-7.1.2-Q16-HDRI\magick.exe" (
    echo ✓ 找到: C:\Program Files (x86)\ImageMagick-7.1.2-Q16-HDRI\magick.exe
    set FOUND=1
)

if %FOUND% EQ 0 (
    echo ✗ 在常见路径中未找到ImageMagick
    echo.
    echo 请从以下地址下载并安装ImageMagick:
    echo https://imagemagick.org/script/download.php#windows
    echo.
    echo 安装时请确保:
    echo 1. 选择"Add application directory to your system path"
    echo 2. 选择"Install development headers and libraries for C and C++"
)

echo.
echo 测试HEIC转换...
if exist "file\Flower.HEIC" (
    echo 找到测试文件: file\Flower.HEIC
    
    REM 尝试使用magick命令
    magick "file\Flower.HEIC" "file\test_magick.jpg" >nul 2>&1
    if %ERRORLEVEL% EQ 0 (
        echo ✓ magick命令转换成功
        if exist "file\test_magick.jpg" (
            for %%A in ("file\test_magick.jpg") do echo   输出文件大小: %%~zA 字节
        )
    ) else (
        echo ✗ magick命令转换失败
    )
    
    REM 尝试使用convert命令
    convert "file\Flower.HEIC" "file\test_convert.jpg" >nul 2>&1
    if %ERRORLEVEL% EQ 0 (
        echo ✓ convert命令转换成功
        if exist "file\test_convert.jpg" (
            for %%A in ("file\test_convert.jpg") do echo   输出文件大小: %%~zA 字节
        )
    ) else (
        echo ✗ convert命令转换失败
    )
    
) else (
    echo ✗ 测试文件不存在: file\Flower.HEIC
)

echo.
pause

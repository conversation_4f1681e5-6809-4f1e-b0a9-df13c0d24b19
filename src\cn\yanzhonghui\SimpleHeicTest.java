package cn.yanz<PERSON><PERSON>;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;

/**
 * Simple HEIC reading test
 */
public class SimpleHeicTest {
    
    public static void main(String[] args) {
        // Test file path
        String heicFilePath = "./file/Flower.HEIC";
        
        System.out.println("=== Simple HEIC Test ===");
        System.out.println("Java version: " + System.getProperty("java.version"));
        System.out.println("Test file: " + heicFilePath);
        
        File heicFile = new File(heicFilePath);
        if (!heicFile.exists()) {
            System.err.println("Test file does not exist: " + heicFilePath);
            return;
        }
        
        System.out.println("File exists, size: " + heicFile.length() + " bytes");
        
        // Scan for plugins
        ImageIO.scanForPlugins();
        
        // Show available readers
        System.out.println("\nAvailable readers:");
        String[] formats = ImageIO.getReaderFormatNames();
        for (String format : formats) {
            System.out.println("- " + format);
        }
        
        // Try to read the file
        System.out.println("\nAttempting to read HEIC file...");
        try {
            BufferedImage image = ImageIO.read(heicFile);
            
            if (image != null) {
                System.out.println("✓ SUCCESS! Image read successfully");
                System.out.println("Dimensions: " + image.getWidth() + "x" + image.getHeight());
                
                // Try to write as PNG
                String outputPath = "./file/test_output.png";
                boolean writeSuccess = ImageIO.write(image, "PNG", new File(outputPath));
                if (writeSuccess) {
                    System.out.println("✓ Successfully wrote PNG: " + outputPath);
                } else {
                    System.out.println("✗ Failed to write PNG");
                }
                
            } else {
                System.out.println("✗ FAILED: ImageIO.read() returned null");
                System.out.println("This means the HEIC format is not supported by available ImageIO plugins");
                
                // Check what we have
                System.out.println("\nDebugging information:");
                System.out.println("Available ImageIO readers for common formats:");
                String[] testFormats = {"JPEG", "PNG", "GIF", "BMP", "HEIF", "HEIC"};
                for (String format : testFormats) {
                    boolean hasReader = ImageIO.getImageReadersByFormatName(format).hasNext();
                    System.out.println("- " + format + ": " + (hasReader ? "✓" : "✗"));
                }
            }
            
        } catch (Exception e) {
            System.out.println("✗ ERROR: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("\n=== Recommendations ===");
        System.out.println("If HEIC reading failed:");
        System.out.println("1. Make sure imageio-heif JAR is in classpath");
        System.out.println("2. Check if native HEIF libraries are installed");
        System.out.println("3. On Windows, install Microsoft HEIF codec from Microsoft Store");
        System.out.println("4. Try using a different HEIC file to test");
    }
}

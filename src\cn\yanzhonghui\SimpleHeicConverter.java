package cn.ya<PERSON><PERSON><PERSON>;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * Simple HEIC converter that avoids ImageIO issues
 * Uses system commands to convert HEIC files
 */
public class SimpleHeicConverter {
    
    public static void main(String[] args) {
        String inputPath = "./file/Flower.HEIC";
        String outputPath = "./file/Flower_simple.jpg";
        
        if (args.length >= 2) {
            inputPath = args[0];
            outputPath = args[1];
        } else if (args.length == 1) {
            inputPath = args[0];
            outputPath = generateOutputPath(inputPath);
        }
        
        System.out.println("Simple HEIC Converter");
        System.out.println("====================");
        System.out.println("Input: " + inputPath);
        System.out.println("Output: " + outputPath);
        System.out.println();
        
        // Check if input file exists
        File inputFile = new File(inputPath);
        if (!inputFile.exists()) {
            System.err.println("ERROR: Input file not found: " + inputPath);
            return;
        }
        
        System.out.println("Input file found, size: " + inputFile.length() + " bytes");
        
        // Try different conversion methods
        boolean success = false;
        
        // Method 1: PowerShell with System.Drawing
        if (!success) {
            System.out.println("\nTrying Method 1: PowerShell with .NET System.Drawing...");
            try {
                convertWithPowerShell(inputPath, outputPath);
                success = true;
                System.out.println("SUCCESS: PowerShell method worked!");
            } catch (Exception e) {
                System.out.println("PowerShell method failed: " + e.getMessage());
            }
        }
        
        // Method 2: ImageMagick
        if (!success) {
            System.out.println("\nTrying Method 2: ImageMagick...");
            try {
                convertWithImageMagick(inputPath, outputPath);
                success = true;
                System.out.println("SUCCESS: ImageMagick method worked!");
            } catch (Exception e) {
                System.out.println("ImageMagick method failed: " + e.getMessage());
            }
        }
        
        // Method 3: FFmpeg
        if (!success) {
            System.out.println("\nTrying Method 3: FFmpeg...");
            try {
                convertWithFFmpeg(inputPath, outputPath);
                success = true;
                System.out.println("SUCCESS: FFmpeg method worked!");
            } catch (Exception e) {
                System.out.println("FFmpeg method failed: " + e.getMessage());
            }
        }
        
        if (success) {
            File outputFile = new File(outputPath);
            if (outputFile.exists()) {
                System.out.println("\nFINAL SUCCESS!");
                System.out.println("Output file: " + outputPath);
                System.out.println("Output size: " + outputFile.length() + " bytes");
            }
        } else {
            System.err.println("\nALL METHODS FAILED!");
            printTroubleshootingInfo();
        }
    }
    
    private static void convertWithPowerShell(String inputPath, String outputPath) throws IOException, InterruptedException {
        String script = String.format(
            "Add-Type -AssemblyName System.Drawing; " +
            "$img = [System.Drawing.Image]::FromFile('%s'); " +
            "$img.Save('%s', [System.Drawing.Imaging.ImageFormat]::Jpeg); " +
            "$img.Dispose(); " +
            "Write-Host 'PowerShell conversion completed'",
            new File(inputPath).getAbsolutePath().replace("\\", "\\\\"),
            new File(outputPath).getAbsolutePath().replace("\\", "\\\\")
        );
        
        executeCommand(new String[]{"powershell.exe", "-Command", script});
    }
    
    private static void convertWithImageMagick(String inputPath, String outputPath) throws IOException, InterruptedException {
        executeCommand(new String[]{"magick", inputPath, outputPath});
    }
    
    private static void convertWithFFmpeg(String inputPath, String outputPath) throws IOException, InterruptedException {
        executeCommand(new String[]{"ffmpeg", "-i", inputPath, "-y", outputPath});
    }
    
    private static void executeCommand(String[] command) throws IOException, InterruptedException {
        ProcessBuilder pb = new ProcessBuilder(command);
        pb.redirectErrorStream(true);
        Process process = pb.start();
        
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
                System.out.println("  " + line);
            }
        }
        
        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new IOException("Command failed with exit code " + exitCode + ": " + output.toString());
        }
    }
    
    private static String generateOutputPath(String inputPath) {
        int lastDotIndex = inputPath.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return inputPath.substring(0, lastDotIndex) + "_converted.jpg";
        } else {
            return inputPath + "_converted.jpg";
        }
    }
    
    private static void printTroubleshootingInfo() {
        System.out.println("\n=== Troubleshooting Guide ===");
        System.out.println("1. Install Microsoft HEIF Image Extensions from Microsoft Store");
        System.out.println("2. Install ImageMagick: https://imagemagick.org/script/download.php#windows");
        System.out.println("3. Install FFmpeg: https://ffmpeg.org/download.html");
        System.out.println("4. Make sure PowerShell execution policy allows scripts");
        System.out.println("5. Try opening the HEIC file in Windows Photos app first");
        System.out.println("6. Check if the HEIC file is corrupted by trying to open it");
    }
}

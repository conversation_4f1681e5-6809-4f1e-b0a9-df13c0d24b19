package cn.yanz<PERSON><PERSON>;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.spi.ImageReaderSpi;
import java.io.File;
import java.util.Iterator;

/**
 * Diagnostic tool to check HEIC/HEIF support
 */
public class HeicDiagnostic {
    
    public static void main(String[] args) {
        System.out.println("=== HEIC/HEIF Support Diagnostic ===");
        System.out.println("Java version: " + System.getProperty("java.version"));
        System.out.println("OS: " + System.getProperty("os.name") + " " + System.getProperty("os.arch"));
        System.out.println();
        
        // Scan for plugins
        ImageIO.scanForPlugins();
        
        // Check available readers
        System.out.println("Available ImageIO readers:");
        String[] readerFormats = ImageIO.getReaderFormatNames();
        boolean hasHeif = false;
        for (String format : readerFormats) {
            System.out.println("- " + format);
            if (format.toLowerCase().contains("heif") || format.toLowerCase().contains("heic")) {
                hasHeif = true;
            }
        }
        System.out.println();
        
        if (hasHeif) {
            System.out.println("✓ HEIF/HEIC reader found!");
        } else {
            System.out.println("✗ No HEIF/HEIC reader found");
        }
        System.out.println();
        
        // Check specific HEIF readers
        System.out.println("Checking for HEIF ImageReader implementations:");
        Iterator<ImageReader> heifReaders = ImageIO.getImageReadersByFormatName("HEIF");
        boolean foundHeifReader = false;
        while (heifReaders.hasNext()) {
            ImageReader reader = heifReaders.next();
            System.out.println("- Found HEIF reader: " + reader.getClass().getName());
            if (reader.getOriginatingProvider() != null) {
                ImageReaderSpi spi = reader.getOriginatingProvider();
                System.out.println("  Description: " + spi.getDescription(null));
                System.out.println("  Vendor: " + spi.getVendorName());
                System.out.println("  Version: " + spi.getVersion());
            }
            foundHeifReader = true;
            reader.dispose();
        }
        
        if (!foundHeifReader) {
            System.out.println("✗ No HEIF ImageReader SPI found");
        }
        System.out.println();
        
        // Test file reading
        String testFile = "./file/Flower.HEIC";
        File heicFile = new File(testFile);
        
        if (heicFile.exists()) {
            System.out.println("Testing file: " + testFile);
            System.out.println("File size: " + heicFile.length() + " bytes");
            
            try {
                System.out.println("Attempting to read with ImageIO.read()...");
                java.awt.image.BufferedImage image = ImageIO.read(heicFile);
                
                if (image != null) {
                    System.out.println("✓ Successfully read HEIC file!");
                    System.out.println("Image dimensions: " + image.getWidth() + "x" + image.getHeight());
                } else {
                    System.out.println("✗ ImageIO.read() returned null");
                    
                    // Try alternative approach
                    System.out.println("Trying alternative reading method...");
                    java.io.FileInputStream fis = null;
                    javax.imageio.stream.ImageInputStream iis = null;
                    ImageReader reader = null;

                    try {
                        fis = new java.io.FileInputStream(heicFile);
                        iis = ImageIO.createImageInputStream(fis);

                        if (iis != null) {
                            Iterator<ImageReader> readers = ImageIO.getImageReaders(iis);
                            if (readers.hasNext()) {
                                reader = readers.next();
                                System.out.println("Found reader: " + reader.getClass().getName());
                                reader.setInput(iis, true, true);
                                image = reader.read(0);
                                if (image != null) {
                                    System.out.println("✓ Successfully read with alternative method!");
                                    System.out.println("Image dimensions: " + image.getWidth() + "x" + image.getHeight());
                                } else {
                                    System.out.println("✗ Alternative method also returned null");
                                }
                            } else {
                                System.out.println("✗ No suitable ImageReader found for this file");
                            }
                        } else {
                            System.out.println("✗ Could not create ImageInputStream");
                        }
                    } catch (Exception altEx) {
                        System.out.println("✗ Alternative method failed: " + altEx.getMessage());
                    } finally {
                        // Clean up resources manually
                        if (reader != null) {
                            try { reader.dispose(); } catch (Exception e) { /* ignore */ }
                        }
                        if (iis != null) {
                            try { iis.close(); } catch (Exception e) { /* ignore */ }
                        }
                        if (fis != null) {
                            try { fis.close(); } catch (Exception e) { /* ignore */ }
                        }
                    }
                }

                // Test PNG writing if we got an image
                if (image != null) {
                    try {
                        String outputPath = "./file/diagnostic_test.png";
                        System.out.println("Testing PNG write to: " + outputPath);
                        boolean writeSuccess = ImageIO.write(image, "PNG", new File(outputPath));
                        if (writeSuccess) {
                            System.out.println("✓ Successfully wrote PNG test file");
                            File testOutput = new File(outputPath);
                            if (testOutput.exists()) {
                                System.out.println("PNG file size: " + testOutput.length() + " bytes");
                            }
                        } else {
                            System.out.println("✗ PNG write returned false");
                        }
                    } catch (Exception writeEx) {
                        System.out.println("✗ Error writing PNG: " + writeEx.getMessage());
                    }
                }

            } catch (Exception e) {
                System.out.println("✗ Error reading file: " + e.getMessage());
                e.printStackTrace();
            }
        } else {
            System.out.println("Test file not found: " + testFile);
        }
        
        System.out.println();
        System.out.println("=== Recommendations ===");
        if (!hasHeif && !foundHeifReader) {
            System.out.println("1. Make sure imageio-heif library is in classpath");
            System.out.println("2. Check if native HEIF libraries are available");
            System.out.println("3. Try running: mvn clean compile exec:java");
            System.out.println("4. On Windows, you may need to install Microsoft HEIF codec");
        }
    }
}

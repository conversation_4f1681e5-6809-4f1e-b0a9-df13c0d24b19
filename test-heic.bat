@echo off
echo HEIC转换测试工具
echo ================

REM 设置classpath - 包含所有必要的JAR文件
set CLASSPATH=target\classes
set CLASSPATH=%CLASSPATH%;D:\maven_repository\com\github\gotson\nightmonkeys\imageio-heif\1.0.0\imageio-heif-1.0.0.jar
set CLASSPATH=%CLASSPATH%;D:\maven_repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar
set CLASSPATH=%CLASSPATH%;D:\maven_repository\com\github\gotson\nightmonkeys\imageio-common\1.0.0\imageio-common-1.0.0.jar
set CLASSPATH=%CLASSPATH%;D:\maven_repository\com\twelvemonkeys\imageio\imageio-core\3.10.1\imageio-core-3.10.1.jar
set CLASSPATH=%CLASSPATH%;D:\maven_repository\com\twelvemonkeys\common\common-lang\3.10.1\common-lang-3.10.1.jar
set CLASSPATH=%CLASSPATH%;D:\maven_repository\com\twelvemonkeys\common\common-io\3.10.1\common-io-3.10.1.jar
set CLASSPATH=%CLASSPATH%;D:\maven_repository\com\twelvemonkeys\common\common-image\3.10.1\common-image-3.10.1.jar
set CLASSPATH=%CLASSPATH%;D:\maven_repository\com\twelvemonkeys\imageio\imageio-metadata\3.10.1\imageio-metadata-3.10.1.jar

echo 当前Java版本:
java -version
echo.

echo 编译Java文件...
javac -cp "%CLASSPATH%" -d target/classes src/cn/yanzhonghui/SimpleHeicTest.java
if %ERRORLEVEL% NEQ 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 编译成功！
echo.

echo 运行简单HEIC测试...
java -cp "%CLASSPATH%" cn.yanzhonghui.SimpleHeicTest

echo.
echo 测试完成！
pause

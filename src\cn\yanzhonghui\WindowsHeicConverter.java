package cn.ya<PERSON><PERSON><PERSON>;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * HEIC converter using Windows PowerShell and .NET APIs
 * This approach uses Windows built-in image processing capabilities
 */
public class WindowsHeicConverter {
    
    public static void main(String[] args) {
        String inputPath;
        String outputPath;
        
        // Handle command line arguments
        if (args.length >= 2) {
            inputPath = args[0];
            outputPath = args[1];
        } else if (args.length == 1) {
            inputPath = args[0];
            outputPath = generateOutputPath(inputPath, "jpg");
        } else {
            inputPath = "./file/Flower.HEIC";
            outputPath = "./file/Flower_converted.jpg";
            System.out.println("Using default file paths for conversion...");
        }
        
        try {
            convertHeicWithPowerShell(inputPath, outputPath);
            System.out.println("SUCCESS: Conversion successful: " + outputPath);
        } catch (Exception e) {
            System.err.println("ERROR: Conversion failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Convert HEIC to JPG using PowerShell and Windows APIs
     */
    public static void convertHeicWithPowerShell(String inputPath, String outputPath) throws IOException, InterruptedException {
        File inputFile = new File(inputPath);
        if (!inputFile.exists()) {
            throw new IOException("Input file does not exist: " + inputPath);
        }
        
        System.out.println("Converting HEIC to JPG using Windows PowerShell...");
        System.out.println("Input: " + inputFile.getAbsolutePath());
        System.out.println("Output: " + new File(outputPath).getAbsolutePath());
        
        // Create output directory if needed
        File outputFile = new File(outputPath);
        File parentDir = outputFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }
        
        // PowerShell script to convert HEIC to JPG
        String powershellScript = String.format(
            "Add-Type -AssemblyName System.Drawing; " +
            "$image = [System.Drawing.Image]::FromFile('%s'); " +
            "$image.Save('%s', [System.Drawing.Imaging.ImageFormat]::Jpeg); " +
            "$image.Dispose(); " +
            "Write-Host 'Conversion completed successfully'",
            inputFile.getAbsolutePath().replace("\\", "\\\\"),
            outputFile.getAbsolutePath().replace("\\", "\\\\")
        );
        
        // Execute PowerShell command
        ProcessBuilder pb = new ProcessBuilder(
            "powershell.exe", 
            "-Command", 
            powershellScript
        );
        
        pb.redirectErrorStream(true);
        Process process = pb.start();
        
        // Read output
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
                System.out.println(line);
            }
        }
        
        // Wait for completion
        int exitCode = process.waitFor();
        
        if (exitCode != 0) {
            throw new IOException("PowerShell conversion failed with exit code: " + exitCode + "\nOutput: " + output.toString());
        }
        
        // Verify output file was created
        if (!outputFile.exists() || outputFile.length() == 0) {
            throw new IOException("Output file was not created or is empty");
        }
        
        System.out.println("Output file size: " + outputFile.length() + " bytes");
    }
    
    /**
     * Alternative method using magick command (if ImageMagick is installed)
     */
    public static void convertHeicWithImageMagick(String inputPath, String outputPath) throws IOException, InterruptedException {
        File inputFile = new File(inputPath);
        if (!inputFile.exists()) {
            throw new IOException("Input file does not exist: " + inputPath);
        }
        
        System.out.println("Converting HEIC to JPG using ImageMagick...");
        
        // Create output directory if needed
        File outputFile = new File(outputPath);
        File parentDir = outputFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }
        
        // Execute magick command
        ProcessBuilder pb = new ProcessBuilder(
            "magick", 
            inputFile.getAbsolutePath(),
            outputFile.getAbsolutePath()
        );
        
        pb.redirectErrorStream(true);
        Process process = pb.start();
        
        // Read output
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println(line);
            }
        }
        
        // Wait for completion
        int exitCode = process.waitFor();
        
        if (exitCode != 0) {
            throw new IOException("ImageMagick conversion failed with exit code: " + exitCode);
        }
        
        // Verify output file was created
        if (!outputFile.exists() || outputFile.length() == 0) {
            throw new IOException("Output file was not created or is empty");
        }
        
        System.out.println("Output file size: " + outputFile.length() + " bytes");
    }
    
    /**
     * Generate output file path based on input file path
     */
    private static String generateOutputPath(String inputPath, String extension) {
        int lastDotIndex = inputPath.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return inputPath.substring(0, lastDotIndex) + "_converted." + extension;
        } else {
            return inputPath + "_converted." + extension;
        }
    }
}

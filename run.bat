@echo off
echo HEIC转PNG转换器
echo ================

REM 检查Java版本
echo 检查Java版本...
java -version
echo.

REM 创建输出目录
if not exist "out\production" mkdir out\production

REM 编译Java文件
echo 编译Java文件...
javac -cp "lib/*" -d out/production src/cn/yanzhonghui/Main.java

if %ERRORLEVEL% NEQ 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 编译成功！
echo.

REM 运行程序
echo 运行HEIC转换程序...
java -cp "lib/*;out/production" cn.yanzhonghui.Main %*

echo.
pause

@echo off
echo Windows HEIC转换器测试
echo ====================

REM 设置classpath
set CLASSPATH=target\classes

echo 编译WindowsHeicConverter...
javac -cp "%CLASSPATH%" -d target/classes src/cn/yanzhonghui/WindowsHeicConverter.java
if %ERRORLEVEL% NEQ 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 编译成功！
echo.

echo 运行Windows HEIC转换器...
java -cp "%CLASSPATH%" cn.yanzhonghui.WindowsHeicConverter

echo.
echo 测试完成！
pause

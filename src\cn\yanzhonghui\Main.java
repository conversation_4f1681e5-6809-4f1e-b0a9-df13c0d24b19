package cn.ya<PERSON><PERSON><PERSON>;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

public class Main {

    public static void main(String[] args) {
        String inputPath;
        String outputPath;

        // Handle command line arguments
        if (args.length >= 2) {
            inputPath = args[0];
            outputPath = args[1];
        } else if (args.length == 1) {
            inputPath = args[0];
            // Auto-generate output filename
            outputPath = generateOutputPath(inputPath);
        } else {
            // Use default paths
            inputPath = "./file/Flower.HEIC";
            outputPath = "./file/Flower.PNG";
            System.out.println("Using default file paths for conversion...");
        }

        convertHeicToPng(inputPath, outputPath);
    }

    /**
     * Convert HEIC file to PNG file
     * @param inputPath HEIC file path
     * @param outputPath PNG output file path
     */
    public static void convertHeicToPng(String inputPath, String outputPath) {
        try {
            // Check if input file exists
            File inputFile = new File(inputPath);
            if (!inputFile.exists()) {
                System.err.println("Input file does not exist: " + inputPath);
                return;
            }

            System.out.println("Starting conversion...");
            System.out.println("Input file: " + inputPath);
            System.out.println("Output file: " + outputPath);
            System.out.println("Current Java version: " + System.getProperty("java.version"));

            // Show available ImageIO readers
            printAvailableImageReaders();

            // Use ImageIO to read HEIC file
            BufferedImage image = ImageIO.read(inputFile);

            if (image == null) {
                System.err.println("Cannot read HEIC file, possible reasons:");
                System.err.println("1. Missing HEIC/HEIF ImageIO plugin");
                System.err.println("2. File format is incorrect or file is corrupted");
                System.err.println("3. Need to install NightMonkeys imageio-heif library");
                System.err.println("\nSuggested solutions:");
                System.err.println("1. Install dependencies with Maven: mvn clean compile");
                System.err.println("2. Or download imageio-heif JAR file to lib directory");
                return;
            }

            // Create output directory if it doesn't exist
            File outputFile = new File(outputPath);
            File parentDir = outputFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            // Write image to PNG file
            boolean success = ImageIO.write(image, "PNG", outputFile);

            if (success) {
                System.out.println("✓ Successfully converted HEIC to PNG!");
                System.out.println("Image dimensions: " + image.getWidth() + "x" + image.getHeight());
                System.out.println("Output file size: " + outputFile.length() + " bytes");
            } else {
                System.err.println("PNG write failed!");
            }

        } catch (IOException e) {
            System.err.println("Error occurred during conversion: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Print available ImageIO reader information
     */
    private static void printAvailableImageReaders() {
        System.out.println("\nAvailable ImageIO readers:");
        String[] readerFormats = ImageIO.getReaderFormatNames();
        for (String format : readerFormats) {
            System.out.println("- " + format);
        }
        System.out.println();
    }

    /**
     * Generate output file path based on input file path
     * @param inputPath Input file path
     * @return PNG output file path
     */
    private static String generateOutputPath(String inputPath) {
        int lastDotIndex = inputPath.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return inputPath.substring(0, lastDotIndex) + ".PNG";
        } else {
            return inputPath + ".PNG";
        }
    }
}

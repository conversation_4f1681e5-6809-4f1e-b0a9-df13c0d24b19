package cn.yanzhong<PERSON>;

import org.im4java.core.*;
import org.im4java.process.ProcessStarter;
import java.io.File;
import java.io.IOException;

public class Main {

    // 可能的ImageMagick安装路径
    private static final String[] POSSIBLE_PATHS = {
        "D:\\Program Files\\ImageMagick-7.1.2-Q16-HDRI",
    };

    public static void main(String[] args) {
        System.out.println("HEIC to PNG Converter using ImageMagick");
        System.out.println("=======================================");

        // 查找ImageMagick安装路径
        String imageMagickPath = findImageMagickPath();
        if (imageMagickPath == null) {
            System.err.println("ERROR: ImageMagick not found!");
            System.err.println("Please install ImageMagick from: https://imagemagick.org/script/download.php#windows");
            System.err.println("Or check if the installation path is correct.");
            return;
        }

        System.out.println("Found ImageMagick at: " + imageMagickPath);

        // 设置输入输出文件路径
        String inputPath = "./file/Flower.HEIC";
        String outputPath = "./file/Flower.PNG";

        // 检查输入文件是否存在
        File inputFile = new File(inputPath);
        if (!inputFile.exists()) {
            System.err.println("ERROR: Input file not found: " + inputFile.getAbsolutePath());
            return;
        }

        System.out.println("Input file: " + inputFile.getAbsolutePath());
        System.out.println("Output file: " + new File(outputPath).getAbsolutePath());
        System.out.println("Input file size: " + inputFile.length() + " bytes");

        try {
            // 尝试使用新版本的magick命令
            convertWithMagick(imageMagickPath, inputPath, outputPath);
        } catch (Exception e) {
            System.err.println("Magick command failed, trying convert command...");
            try {
                // 回退到旧版本的convert命令
                convertWithConvert(imageMagickPath, inputPath, outputPath);
            } catch (Exception e2) {
                System.err.println("Both magick and convert commands failed!");
                e.printStackTrace();
                e2.printStackTrace();
            }
        }
    }

    /**
     * 查找ImageMagick安装路径
     */
    private static String findImageMagickPath() {
        for (String path : POSSIBLE_PATHS) {
            File dir = new File(path);
            if (dir.exists() && dir.isDirectory()) {
                // 检查是否存在magick.exe或convert.exe
                File magickExe = new File(dir, "magick.exe");
                File convertExe = new File(dir, "convert.exe");
                if (magickExe.exists() || convertExe.exists()) {
                    return path;
                }
            }
        }
        return null;
    }

    /**
     * 使用magick命令转换（ImageMagick 7+）
     */
    private static void convertWithMagick(String imageMagickPath, String inputPath, String outputPath)
            throws IOException, InterruptedException, IM4JavaException {
        System.out.println("Using magick command...");

        ConvertCmd cmd = new ConvertCmd();
        cmd.setSearchPath(imageMagickPath);

        // 对于ImageMagick 7+，使用magick命令
        ProcessStarter.setGlobalSearchPath(imageMagickPath);

        IMOperation op = new IMOperation();
        op.addImage(inputPath);
        op.addImage(outputPath);

        cmd.run(op);

        // 检查输出文件
        File outputFile = new File(outputPath);
        if (outputFile.exists()) {
            System.out.println("SUCCESS: Conversion completed!");
            System.out.println("Output file size: " + outputFile.length() + " bytes");
        } else {
            throw new IOException("Output file was not created");
        }
    }

    /**
     * 使用convert命令转换（ImageMagick 6）
     */
    private static void convertWithConvert(String imageMagickPath, String inputPath, String outputPath)
            throws IOException, InterruptedException, IM4JavaException {
        System.out.println("Using convert command...");

        ConvertCmd cmd = new ConvertCmd();
        cmd.setSearchPath(imageMagickPath);

        IMOperation op = new IMOperation();
        op.addImage(inputPath);
        op.addImage(outputPath);

        cmd.run(op);

        // 检查输出文件
        File outputFile = new File(outputPath);
        if (outputFile.exists()) {
            System.out.println("SUCCESS: Conversion completed!");
            System.out.println("Output file size: " + outputFile.length() + " bytes");
        } else {
            throw new IOException("Output file was not created");
        }
    }
}

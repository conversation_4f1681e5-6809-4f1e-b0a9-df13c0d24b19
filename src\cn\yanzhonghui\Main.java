package cn.yanzhong<PERSON>;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

public class Main {

    public static void main(String[] args) {
        String inputPath;
        String outputPath;

        // 处理命令行参数
        if (args.length >= 2) {
            inputPath = args[0];
            outputPath = args[1];
        } else if (args.length == 1) {
            inputPath = args[0];
            // 自动生成输出文件名
            outputPath = generateOutputPath(inputPath);
        } else {
            // 使用默认路径
            inputPath = "./file/Flower.HEIC";
            outputPath = "./file/Flower.PNG";
            System.out.println("使用默认文件路径进行转换...");
        }

        convertHeicToPng(inputPath, outputPath);
    }

    /**
     * 将HEIC文件转换为PNG文件
     * @param inputPath HEIC文件路径
     * @param outputPath PNG输出文件路径
     */
    public static void convertHeicToPng(String inputPath, String outputPath) {
        try {
            // 检查输入文件是否存在
            File inputFile = new File(inputPath);
            if (!inputFile.exists()) {
                System.err.println("输入文件不存在: " + inputPath);
                return;
            }

            System.out.println("开始转换...");
            System.out.println("输入文件: " + inputPath);
            System.out.println("输出文件: " + outputPath);

            // 使用ImageIO读取HEIC文件
            BufferedImage image = ImageIO.read(inputFile);

            if (image == null) {
                System.err.println("无法读取HEIC文件，可能是因为：");
                System.err.println("1. Java版本不支持HEIC格式（需要Java 9+）");
                System.err.println("2. 系统缺少HEIC解码器支持");
                System.err.println("3. 文件格式不正确或文件损坏");
                System.err.println("当前Java版本: " + System.getProperty("java.version"));
                return;
            }

            // 创建输出目录（如果不存在）
            File outputFile = new File(outputPath);
            File parentDir = outputFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            // 将图像写入PNG文件
            boolean success = ImageIO.write(image, "PNG", outputFile);

            if (success) {
                System.out.println("✓ 成功将HEIC转换为PNG！");
                System.out.println("图像尺寸: " + image.getWidth() + "x" + image.getHeight());
                System.out.println("输出文件大小: " + outputFile.length() + " bytes");
            } else {
                System.err.println("PNG写入失败！");
            }

        } catch (IOException e) {
            System.err.println("转换过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 根据输入文件路径生成输出文件路径
     * @param inputPath 输入文件路径
     * @return PNG输出文件路径
     */
    private static String generateOutputPath(String inputPath) {
        int lastDotIndex = inputPath.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return inputPath.substring(0, lastDotIndex) + ".PNG";
        } else {
            return inputPath + ".PNG";
        }
    }
}

# HEIC转PNG转换器

本项目提供了多种将HEIC文件转换为PNG文件的Java解决方案。

## 解决方案概述

### 方案1: 使用NightMonkeys ImageIO-HEIF库 (推荐)

这是最纯粹的Java解决方案，使用开源的imageio-heif库。

**优点:**
- 纯Java解决方案
- 不需要外部命令行工具
- 性能好

**缺点:**
- 需要下载额外的JAR依赖
- 可能需要本地库支持

**使用方法:**

1. 使用Maven安装依赖:
```bash
mvn clean compile
mvn exec:java -Dexec.mainClass="cn.yanzhonghui.Main"
```

2. 或者手动下载JAR文件:
```bash
# 运行下载脚本
download-heif-lib.bat

# 编译和运行
run.bat
```

### 方案2: 使用libheif命令行工具

使用系统安装的libheif命令行工具进行转换。

**优点:**
- 不需要Java库依赖
- libheif是官方HEIF实现
- 转换质量高

**缺点:**
- 需要安装外部工具
- 跨平台兼容性需要考虑

**安装libheif:**

Windows:
- 下载libheif预编译二进制文件
- 或使用MSYS2: `pacman -S mingw-w64-x86_64-libheif`

Linux:
```bash
sudo apt install libheif-examples  # Ubuntu/Debian
sudo yum install libheif-tools     # CentOS/RHEL
```

macOS:
```bash
brew install libheif
```

**使用方法:**
```bash
javac src/cn/yanzhonghui/HeicConverterWithLibheif.java
java -cp src cn.yanzhonghui.HeicConverterWithLibheif input.heic output.png
```

## 文件说明

- `Main.java` - 使用ImageIO和imageio-heif库的主要实现
- `HeicConverterWithLibheif.java` - 使用libheif命令行工具的实现
- `pom.xml` - Maven项目配置文件
- `download-heif-lib.bat` - 下载所需JAR文件的脚本
- `run.bat` - 编译和运行的便捷脚本

## 使用示例

### 基本用法
```bash
# 使用默认文件
java -cp "lib/*;out/production" cn.yanzhonghui.Main

# 指定输入文件（自动生成输出文件名）
java -cp "lib/*;out/production" cn.yanzhonghui.Main input.heic

# 指定输入和输出文件
java -cp "lib/*;out/production" cn.yanzhonghui.Main input.heic output.png
```

### 批量转换
可以编写脚本来批量转换多个文件：

```bash
for %%f in (*.heic) do (
    java -cp "lib/*;out/production" cn.yanzhonghui.Main "%%f" "%%~nf.png"
)
```

## 故障排除

### 问题1: "无法读取HEIC文件"
- 确保已正确安装imageio-heif库
- 检查Java版本是否为9+
- 验证HEIC文件是否损坏

### 问题2: "找不到heif-convert命令"
- 安装libheif-examples包
- 确保命令行工具在PATH中

### 问题3: 编译错误
- 检查Java版本
- 确保所有依赖JAR文件在lib目录中
- 验证classpath设置

## 性能对比

| 方案 | 转换速度 | 内存使用 | 依赖复杂度 |
|------|----------|----------|------------|
| ImageIO-HEIF | 快 | 中等 | 中等 |
| libheif | 很快 | 低 | 低 |

## 许可证

本项目使用MIT许可证。依赖库请查看各自的许可证。

# PowerShell script to convert HEIC to JPG
param(
    [string]$InputPath = ".\file\Flower.HEIC",
    [string]$OutputPath = ".\file\Flower_converted.jpg"
)

Write-Host "HEIC to JPG Converter using PowerShell"
Write-Host "====================================="

# Check if input file exists
if (-not (Test-Path $InputPath)) {
    Write-Error "Input file not found: $InputPath"
    exit 1
}

Write-Host "Input file: $InputPath"
Write-Host "Output file: $OutputPath"

# Get input file info
$inputFile = Get-Item $InputPath
Write-Host "Input file size: $($inputFile.Length) bytes"

# Create output directory if it doesn't exist
$outputDir = Split-Path $OutputPath -Parent
if ($outputDir -and -not (Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
}

try {
    # Load System.Drawing assembly
    Add-Type -AssemblyName System.Drawing
    
    Write-Host "Loading image..."
    # Load the HEIC image
    $image = [System.Drawing.Image]::FromFile((Resolve-Path $InputPath).Path)
    
    Write-Host "Image loaded successfully!"
    Write-Host "Image dimensions: $($image.Width)x$($image.Height)"
    
    Write-Host "Converting to JPG..."
    # Save as JPG
    $image.Save((Join-Path (Get-Location) $OutputPath), [System.Drawing.Imaging.ImageFormat]::Jpeg)
    
    # Clean up
    $image.Dispose()
    
    # Check if output file was created
    if (Test-Path $OutputPath) {
        $outputFile = Get-Item $OutputPath
        Write-Host "SUCCESS: Conversion completed!"
        Write-Host "Output file size: $($outputFile.Length) bytes"
    } else {
        Write-Error "Output file was not created"
        exit 1
    }
    
} catch {
    Write-Error "Conversion failed: $($_.Exception.Message)"
    Write-Host ""
    Write-Host "Possible solutions:"
    Write-Host "1. Install Microsoft HEIF Image Extensions from Microsoft Store"
    Write-Host "2. Make sure the HEIC file is not corrupted"
    Write-Host "3. Try opening the file in Windows Photos app first"
    exit 1
}

Write-Host "Conversion completed successfully!"
